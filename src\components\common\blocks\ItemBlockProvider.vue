<script setup lang="ts">
import { provide, watch } from 'vue';
import { createItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';

const props = defineProps<{
  blockId: number;
  itemBlock?: ItemBlock;
}>();

// Create the store for this block, passing the itemBlock data
const blockStore = createItemBlockStore(props.blockId, props.itemBlock);
const store = blockStore();

// Watch for changes in itemBlock prop and sync the store
watch(
  () => props.itemBlock,
  (newItemBlock) => {
    if (newItemBlock) {
      store.syncWithItemBlock(newItemBlock);
    }
  },
  { deep: true, immediate: false }, // Don't run immediately since store is already initialized
);

// Provide the store to child components
provide('blockStore', store);
</script>

<template>
  <slot />
</template>
